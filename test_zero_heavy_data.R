#!/usr/bin/env Rscript
# 测试含有大量零值数据的箱线图显示
# Test boxplot display for zero-heavy data

library(ggplot2)
library(dplyr)

# 模拟CAST的实际数据分布
set.seed(123)

# Simple VH组：大部分是0，只有少数非零值
simple_vh_data <- c(rep(0, 12), 0.0729)  # 12个0，1个0.0729

# SRD+VH组：也有很多0，但有更多变异
srd_vh_data <- c(rep(0, 8), 0.153, 0.181, 0.250, 0.368)  # 8个0，4个非零值

# 创建测试数据
test_data <- data.frame(
  Group = c(rep("Simple VH", 13), rep("SRD+VH", 12)),
  Expression = c(simple_vh_data, srd_vh_data)
)

# 添加抖动位置
test_data <- test_data %>%
  mutate(
    Group_Numeric = ifelse(Group == "Simple VH", 1, 2),
    jitter_offset = runif(n(), -0.2, 0.2),
    x_jittered = Group_Numeric + jitter_offset
  )

# 检查统计信息
cat("Simple VH组统计:\n")
simple_stats <- test_data %>% filter(Group == "Simple VH") %>% pull(Expression)
cat("最小值:", min(simple_stats), "\n")
cat("Q1:", quantile(simple_stats, 0.25), "\n")
cat("中位数:", median(simple_stats), "\n")
cat("Q3:", quantile(simple_stats, 0.75), "\n")
cat("最大值:", max(simple_stats), "\n\n")

cat("SRD+VH组统计:\n")
srd_stats <- test_data %>% filter(Group == "SRD+VH") %>% pull(Expression)
cat("最小值:", min(srd_stats), "\n")
cat("Q1:", quantile(srd_stats, 0.25), "\n")
cat("中位数:", median(srd_stats), "\n")
cat("Q3:", quantile(srd_stats, 0.75), "\n")
cat("最大值:", max(srd_stats), "\n\n")

# 设置颜色
colors <- c("Simple VH" = "#1F78B4", "SRD+VH" = "#E31A1C")

# 创建改进的箱线图
p1 <- ggplot(test_data, aes(x = Group, y = Expression, fill = Group)) +
  geom_violin(alpha = 0.5, trim = FALSE, scale = "width", width = 0.6) +
  geom_boxplot(width = 0.25, alpha = 1, outlier.shape = NA, 
               color = "black", linewidth = 1.0, fill = "white") +
  geom_point(aes(x = x_jittered), size = 3, alpha = 0.8, stroke = 1, shape = 21, color = "black") +
  scale_fill_manual(values = colors) +
  labs(title = "含大量零值数据的箱线图测试", 
       subtitle = "Simple VH组：12个0值 + 1个0.0729；SRD+VH组：8个0值 + 4个非零值",
       x = "组别", y = "表达水平") +
  theme_bw() +
  theme(legend.position = "none")

# 保存测试图
ggsave("test_zero_heavy_boxplot.png", plot = p1, width = 10, height = 6, dpi = 300)

cat("测试图已保存: test_zero_heavy_boxplot.png\n")
cat("这种数据分布下，Simple VH组的箱线图可能会很窄，因为Q1=中位数=Q3=0\n")
cat("但箱线图仍然应该显示，只是可能看起来像一条线\n")
