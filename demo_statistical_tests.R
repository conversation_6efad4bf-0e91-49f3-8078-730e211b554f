#!/usr/bin/env Rscript
# 演示统计检验功能
# Demo statistical tests functionality

library(ggplot2)
library(dplyr)

set.seed(123)

# 创建演示数据
demo_data <- data.frame(
  Group = rep(c("Simple VH", "SRD+VH", "Baseline", "Post-anti-VEGF"), each = 15),
  Expression = c(
    rnorm(15, mean = 2, sd = 0.5),    # Simple VH
    rnorm(15, mean = 2.8, sd = 0.6),  # SRD+VH (显著不同)
    rnorm(15, mean = 3, sd = 0.4),    # Baseline
    rnorm(15, mean = 2.2, sd = 0.5)   # Post-anti-VEGF (显著不同)
  ),
  Pair_ID = c(
    rep(NA, 30),                      # 基线比较不需要配对
    rep(1:15, 2)                      # 治疗比较需要配对
  )
)

# 添加抖动位置
demo_data <- demo_data %>%
  mutate(
    Group_Numeric = case_when(
      Group == "Simple VH" ~ 1,
      Group == "SRD+VH" ~ 2,
      Group == "Baseline" ~ 3,
      Group == "Post-anti-VEGF" ~ 4
    ),
    jitter_offset = runif(n(), -0.2, 0.2),
    x_jittered = Group_Numeric + jitter_offset
  )

# 设置颜色
colors <- c(
  "Simple VH" = "#1F78B4",      # 蓝色 - 基线比较
  "SRD+VH" = "#1F78B4",        # 蓝色 - 基线比较
  "Baseline" = "#E31A1C",      # 红色 - 治疗比较
  "Post-anti-VEGF" = "#E31A1C" # 红色 - 治疗比较
)

# 创建图表
p <- ggplot(demo_data, aes(x = Group, y = Expression, fill = Group)) +
  geom_violin(alpha = 0.5, trim = FALSE, scale = "width", width = 0.6) +
  geom_boxplot(width = 0.25, alpha = 1, outlier.shape = NA, 
               color = "black", linewidth = 1.0, fill = "white") +
  geom_point(aes(x = x_jittered), size = 3, alpha = 0.8, stroke = 1, shape = 21, color = "black") +
  scale_fill_manual(values = colors) +
  labs(title = "统计检验演示图", 
       subtitle = "Simple VH vs SRD+VH (独立t检验) | Baseline vs Post-anti-VEGF (配对t检验)",
       x = "组别", y = "表达水平") +
  theme_bw() +
  theme(
    legend.position = "none",
    axis.text.x = element_text(angle = 45, hjust = 1),
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 10)
  )

# 手动计算并添加统计检验结果

# 基线比较 (Simple VH vs SRD+VH) - 独立t检验
simple_vh_values <- demo_data %>% filter(Group == "Simple VH") %>% pull(Expression)
srd_vh_values <- demo_data %>% filter(Group == "SRD+VH") %>% pull(Expression)
t_test_baseline <- t.test(simple_vh_values, srd_vh_values, paired = FALSE)
p_val_baseline <- t_test_baseline$p.value

# 计算趋势
mean_simple_vh <- mean(simple_vh_values, na.rm = TRUE)
mean_srd_vh <- mean(srd_vh_values, na.rm = TRUE)
trend_baseline <- if (mean_srd_vh > mean_simple_vh) "up" else "down"

# 格式化p值 (保留3位小数)
p_text_baseline <- sprintf("p = %.3f (%s)", p_val_baseline, trend_baseline)

# 治疗比较 (Baseline vs Post-anti-VEGF) - 配对t检验
baseline_values <- demo_data %>% filter(Group == "Baseline") %>% arrange(Pair_ID) %>% pull(Expression)
post_values <- demo_data %>% filter(Group == "Post-anti-VEGF") %>% arrange(Pair_ID) %>% pull(Expression)
t_test_treatment <- t.test(baseline_values, post_values, paired = TRUE)
p_val_treatment <- t_test_treatment$p.value

# 计算趋势
mean_baseline <- mean(baseline_values, na.rm = TRUE)
mean_post <- mean(post_values, na.rm = TRUE)
trend_treatment <- if (mean_post > mean_baseline) "up" else "down"

# 格式化p值 (保留3位小数)
p_text_treatment <- sprintf("p = %.3f (%s)", p_val_treatment, trend_treatment)

# 添加统计检验标注 (放在坐标系真正的顶端)
# 获取ggplot的y轴范围
y_limits <- ggplot_build(p)$layout$panel_params[[1]]$y.range
y_top <- y_limits[2]  # y轴的最大值（坐标系顶端）

# 基线比较标注 (坐标系顶端)
p <- p + annotate("text", x = 1.5, y = y_top * 0.95,
                 label = p_text_baseline, size = 3.5, hjust = 0.5,
                 color = "black", fontface = "bold")

# 治疗比较标注 (坐标系顶端)
p <- p + annotate("text", x = 3.5, y = y_top * 0.95,
                 label = p_text_treatment, size = 3.5, hjust = 0.5,
                 color = "black", fontface = "bold")

# 保存图表
ggsave("demo_statistical_tests.png", plot = p, width = 10, height = 6, dpi = 300)

cat("✅ 统计检验演示图已生成：demo_statistical_tests.png\n\n")
cat("📊 统计检验结果:\n")
cat("基线比较 (Simple VH vs SRD+VH):", p_text_baseline, "\n")
cat("治疗比较 (Baseline vs Post-anti-VEGF):", p_text_treatment, "\n\n")
cat("🔬 检验方法:\n")
cat("- Simple VH vs SRD+VH: 独立t检验 (unpaired t-test)\n")
cat("- Baseline vs Post-anti-VEGF: 配对t检验 (paired t-test)\n")
