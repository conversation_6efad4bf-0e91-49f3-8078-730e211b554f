#!/usr/bin/env Rscript
# Figure 4 Analysis Script - R Version
# 基于R语言的Figure 4分析脚本：3个Aggravation物质的四小提琴图
# 
# Author: AI Assistant
# Date: 2025-08-03
# Version: 1.0

# =============================================================================
# 1. 环境准备和包加载
# =============================================================================

# 检查并安装必要的包
required_packages <- c(
  "readxl",        # Excel文件读取
  "dplyr",         # 数据处理
  "tibble",        # 数据框操作
  "tidyr",         # 数据重塑
  "stringr",       # 字符串处理
  "ggplot2",       # 基础绘图
  "ggpubr",        # 发表质量图表
  "cowplot",       # 图表组合
  "scales"         # 坐标轴格式化
)

# 安装缺失的包
new_packages <- required_packages[!(required_packages %in% installed.packages()[,"Package"])]
if(length(new_packages)) {
  install.packages(new_packages, repos = "https://cran.rstudio.com/")
}

# 加载包
suppressPackageStartupMessages({
  library(readxl)
  library(dplyr)
  library(tibble)
  library(tidyr)
  library(stringr)
  library(ggplot2)
  library(ggpubr)
  library(cowplot)
  library(scales)
  library(grid)
})

# =============================================================================
# 2. 设置绘图主题和颜色（与Figure 1和2保持一致）
# =============================================================================

# 设置期刊发表质量的主题
theme_publication <- theme_bw() +
  theme(
    text = element_text(size = 12),
    axis.title = element_text(size = 14, face = "bold"),
    axis.text = element_text(size = 12),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    legend.text = element_text(size = 12),
    legend.title = element_text(size = 12, face = "bold"),
    panel.grid.major = element_line(color = "grey90", linewidth = 0.5),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(color = "black", linewidth = 1),
    strip.background = element_rect(fill = "white", color = "black"),
    strip.text = element_text(size = 12, face = "bold")
  )

# 设置颜色方案（两种颜色：基线比较 vs 治疗比较）
colors <- list(
  baseline_comparison = "#1F78B4",  # 蓝色 - 基线比较（Simple VH & SRD+VH）
  treatment_comparison = "#E31A1C"  # 红色 - 治疗比较（Baseline & Post-anti-VEGF）
)

cat("绘图环境设置完成\n")

# =============================================================================
# 3. 数据加载和预处理
# =============================================================================

cat("正在加载数据...\n")

# 加载基线数据 (Figure 1: Simple VH vs SRD+VH)
baseline_protein <- read_excel("基线蛋白组学(1-stage2_vs_1-stage1_annotation).xlsx")
baseline_metabolite <- read_excel("基线代谢组学（1-stage2_vs_1-stage1_info）.xlsx")
baseline_group <- read_excel("基线分组情况（1-stage2_vs_1-stage1_group）.xlsx")

# 加载纵向数据 (Figure 2: Baseline vs Post-anti-VEGF)
longitudinal_protein <- read_excel("纵向蛋白组学（2_vs_1_annotation）.xlsx")
longitudinal_metabolite <- read_excel("纵向代谢组学（2_vs_1_info）.xlsx")
longitudinal_group <- read_excel("纵向分组情况（2_vs_1_group）.xlsx")

cat("数据加载完成\n")

# =============================================================================
# 4. 提取目标分子的表达数据
# =============================================================================

# 目标分子
target_molecules <- c("CAST", "GALNS", "3-Hydroxypropanoic Acid")

cat("正在提取目标分子的表达数据...\n")

# 函数：提取分子表达数据
extract_molecule_data <- function(data, group_data, molecule_name, molecule_type = "protein") {
  
  # 获取样本列（包含"--"的列）
  sample_cols <- grep("--", colnames(data), value = TRUE)
  
  if (molecule_type == "protein") {
    # 蛋白质数据：根据Gene列查找
    molecule_row <- data %>% filter(Gene == molecule_name)
    if (nrow(molecule_row) == 0) {
      cat(sprintf("警告：未找到蛋白质 %s\n", molecule_name))
      return(NULL)
    }
  } else {
    # 代谢物数据：根据Compounds列查找
    molecule_row <- data %>% filter(Compounds == molecule_name)
    if (nrow(molecule_row) == 0) {
      cat(sprintf("警告：未找到代谢物 %s\n", molecule_name))
      return(NULL)
    }
  }
  
  # 提取表达数据
  expression_data <- molecule_row %>%
    select(all_of(sample_cols)) %>%
    pivot_longer(cols = everything(), names_to = "Sample", values_to = "Expression") %>%
    mutate(Expression = as.numeric(Expression))
  
  # 添加分组信息
  expression_data <- expression_data %>%
    left_join(group_data %>% select(Sample, Group), by = "Sample") %>%
    mutate(Molecule = molecule_name)
  
  return(expression_data)
}

# 提取基线数据（Simple VH vs SRD+VH）
baseline_cast <- extract_molecule_data(baseline_protein, baseline_group, "CAST", "protein")
baseline_galns <- extract_molecule_data(baseline_protein, baseline_group, "GALNS", "protein")
baseline_3hp <- extract_molecule_data(baseline_metabolite, baseline_group, "3-Hydroxypropanoic Acid", "metabolite")

# 提取纵向数据（Baseline vs Post-anti-VEGF）
longitudinal_cast <- extract_molecule_data(longitudinal_protein, longitudinal_group, "CAST", "protein")
longitudinal_galns <- extract_molecule_data(longitudinal_protein, longitudinal_group, "GALNS", "protein")
longitudinal_3hp <- extract_molecule_data(longitudinal_metabolite, longitudinal_group, "3-Hydroxypropanoic Acid", "metabolite")

cat("目标分子表达数据提取完成\n")

# =============================================================================
# 5. 创建小提琴图函数
# =============================================================================

create_violin_plot <- function(baseline_data, longitudinal_data, molecule_name, panel_label) {

  cat(sprintf("正在创建 %s 的小提琴图...\n", molecule_name))

  # 检查数据是否存在
  if (is.null(baseline_data) || is.null(longitudinal_data)) {
    cat(sprintf("警告：%s 的数据不完整，跳过\n", molecule_name))
    return(NULL)
  }

  # 检查数据点数量（静默）
  baseline_summary <- baseline_data %>% count(Group)
  longitudinal_summary <- longitudinal_data %>% count(Group)

  # 准备基线数据（Simple VH vs SRD+VH）
  baseline_plot_data <- baseline_data %>%
    mutate(
      Group_Label = case_when(
        Group == "1-stage1" ~ "Simple VH",
        Group == "1-stage2" ~ "SRD+VH",
        TRUE ~ as.character(Group)
      ),
      Comparison = "Baseline Comparison",
      Group_Order = case_when(
        Group_Label == "Simple VH" ~ 1,
        Group_Label == "SRD+VH" ~ 2
      )
    )

  # 准备纵向数据（Baseline vs Post-anti-VEGF）
  longitudinal_plot_data <- longitudinal_data %>%
    mutate(
      Group_Label = case_when(
        Group == 1 ~ "Baseline",
        Group == 2 ~ "Post-anti-VEGF",
        TRUE ~ as.character(Group)
      ),
      Comparison = "Treatment Comparison",
      Group_Order = case_when(
        Group_Label == "Baseline" ~ 3,
        Group_Label == "Post-anti-VEGF" ~ 4
      ),
      # 为配对t检验添加配对ID（基于样本名称）
      Pair_ID = str_extract(Sample, "^[0-9]+")
    )

  # 合并数据
  combined_data <- rbind(
    baseline_plot_data %>%
      select(Sample, Expression, Group_Label, Comparison, Group_Order) %>%
      mutate(Pair_ID = NA),  # 基线比较不需要配对ID
    longitudinal_plot_data %>%
      select(Sample, Expression, Group_Label, Comparison, Group_Order, Pair_ID)
  )

  # 设置因子顺序
  combined_data$Group_Label <- factor(combined_data$Group_Label,
                                     levels = c("Simple VH", "SRD+VH", "Baseline", "Post-anti-VEGF"))

  # 为所有数据点添加抖动位置
  set.seed(123)  # 确保抖动位置可重现
  combined_data <- combined_data %>%
    mutate(
      Group_Numeric = case_when(
        Group_Label == "Simple VH" ~ 1,
        Group_Label == "SRD+VH" ~ 2,
        Group_Label == "Baseline" ~ 3,
        Group_Label == "Post-anti-VEGF" ~ 4
      ),
      jitter_offset = runif(n(), -0.2, 0.2),
      x_jittered = Group_Numeric + jitter_offset
    )

  # 创建箱线小提琴散点图组合
  p <- ggplot(combined_data, aes(x = Group_Label, y = Expression, fill = Group_Label)) +
    # 小提琴图 - 显示数据分布形状（调窄宽度）
    geom_violin(alpha = 0.5, trim = FALSE, scale = "width", width = 0.6) +
    # 箱线图 - 显示统计摘要（强制显示，即使数据重叠）
    geom_boxplot(width = 0.25, alpha = 1, outlier.shape = NA,
                 color = "black", linewidth = 1.0, fill = "white",
                 varwidth = FALSE, notch = FALSE, coef = 1.5) +
    # 散点 - 显示原始数据点
    geom_point(aes(x = x_jittered), size = 3, alpha = 0.8, stroke = 1, shape = 21, color = "black") +
    scale_fill_manual(values = c("Simple VH" = colors$baseline_comparison,
                                "SRD+VH" = colors$baseline_comparison,
                                "Baseline" = colors$treatment_comparison,
                                "Post-anti-VEGF" = colors$treatment_comparison)) +
    labs(x = "", y = "Expression Level", title = molecule_name) +
    theme_publication +
    theme(
      legend.position = "none",
      axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
      plot.title = element_text(size = 14, face = "bold"),
      plot.margin = margin(t = 40, r = 5, b = 5, l = 5, unit = "pt")  # 增加顶部边距为字母标签留空间
    )

  # 添加配对连线（仅对纵向数据）
  if (!is.null(longitudinal_data) && nrow(longitudinal_data) > 0) {
    # 从combined_data中提取纵向数据的抖动位置
    longitudinal_jittered <- combined_data %>%
      filter(Group_Label %in% c("Baseline", "Post-anti-VEGF")) %>%
      # 提取患者ID
      mutate(Patient_ID = str_extract(Sample, "^[^-]+")) %>%
      group_by(Patient_ID) %>%
      filter(n() == 2) %>%  # 只保留有配对数据的患者
      ungroup()

    # 添加连线 - 连接实际的散点位置
    if (nrow(longitudinal_jittered) > 0) {
      p <- p + geom_line(data = longitudinal_jittered,
                        aes(x = x_jittered, y = Expression, group = Patient_ID),
                        alpha = 0.5, color = "gray30", linewidth = 0.8, inherit.aes = FALSE)
    }
  }

  # 手动计算统计检验并添加到图表

  # 计算基线比较的t检验 (Simple VH vs SRD+VH)
  baseline_data_for_stats <- combined_data %>%
    filter(Group_Label %in% c("Simple VH", "SRD+VH"))

  if (nrow(baseline_data_for_stats) > 0) {
    simple_vh_values <- baseline_data_for_stats %>%
      filter(Group_Label == "Simple VH") %>%
      pull(Expression)
    srd_vh_values <- baseline_data_for_stats %>%
      filter(Group_Label == "SRD+VH") %>%
      pull(Expression)

    if (length(simple_vh_values) > 1 && length(srd_vh_values) > 1) {
      t_test_baseline <- t.test(simple_vh_values, srd_vh_values, paired = FALSE)
      p_val_baseline <- t_test_baseline$p.value

      # 计算趋势 (Simple VH vs SRD+VH)
      mean_simple_vh <- mean(simple_vh_values, na.rm = TRUE)
      mean_srd_vh <- mean(srd_vh_values, na.rm = TRUE)
      trend_baseline <- if (mean_srd_vh > mean_simple_vh) "up" else "down"

      # 格式化p值 (保留3位小数)
      p_text_baseline <- sprintf("p = %.3f (%s)", p_val_baseline, trend_baseline)

      # 获取ggplot的y轴范围
      y_limits <- ggplot_build(p)$layout$panel_params[[1]]$y.range
      y_top <- y_limits[2]  # y轴的最大值（坐标系顶端）

      # 添加基线比较的p值标注 (放在坐标系真正的顶端)
      p <- p + annotate("text", x = 1.5, y = y_top * 0.95,
                       label = p_text_baseline, size = 3.5, hjust = 0.5,
                       color = "black", fontface = "bold")
    }
  }

  # 计算治疗比较的配对t检验 (Baseline vs Post-anti-VEGF)
  treatment_data_for_stats <- combined_data %>%
    filter(Group_Label %in% c("Baseline", "Post-anti-VEGF")) %>%
    filter(!is.na(Pair_ID))

  if (nrow(treatment_data_for_stats) > 0) {
    # 确保数据是配对的
    paired_data <- treatment_data_for_stats %>%
      group_by(Pair_ID) %>%
      filter(n() == 2) %>%  # 只保留有完整配对的数据
      ungroup()

    if (nrow(paired_data) > 2) {
      baseline_values <- paired_data %>%
        filter(Group_Label == "Baseline") %>%
        arrange(Pair_ID) %>%
        pull(Expression)
      post_values <- paired_data %>%
        filter(Group_Label == "Post-anti-VEGF") %>%
        arrange(Pair_ID) %>%
        pull(Expression)

      if (length(baseline_values) == length(post_values) && length(baseline_values) > 1) {
        t_test_treatment <- t.test(baseline_values, post_values, paired = TRUE)
        p_val_treatment <- t_test_treatment$p.value

        # 计算趋势 (Baseline vs Post-anti-VEGF)
        mean_baseline <- mean(baseline_values, na.rm = TRUE)
        mean_post <- mean(post_values, na.rm = TRUE)
        trend_treatment <- if (mean_post > mean_baseline) "up" else "down"

        # 格式化p值 (保留3位小数)
        p_text_treatment <- sprintf("p = %.3f (%s)", p_val_treatment, trend_treatment)

        # 添加治疗比较的p值标注 (放在坐标系真正的顶端)
        p <- p + annotate("text", x = 3.5, y = y_top * 0.95,
                         label = p_text_treatment, size = 3.5, hjust = 0.5,
                         color = "black", fontface = "bold")
      }
    }
  }

  return(p)
}

# =============================================================================
# 6. 创建所有小提琴图
# =============================================================================

cat("正在创建所有小提琴图...\n")

# 创建三个小提琴图
plot_A <- create_violin_plot(baseline_cast, longitudinal_cast, "CAST", "A")
plot_B <- create_violin_plot(baseline_galns, longitudinal_galns, "GALNS", "B") 
plot_C <- create_violin_plot(baseline_3hp, longitudinal_3hp, "3-Hydroxypropanoic Acid", "C")

# =============================================================================
# 7. 整合所有图表
# =============================================================================

cat("正在整合所有图表...\n")

# 水平排列三个图，并添加面板标签（放在坐标系外面）
final_plot <- plot_grid(
  plot_A, plot_B, plot_C,
  ncol = 3,
  rel_widths = c(1, 1, 1),
  align = "hv",
  labels = c("A", "B", "C"),
  label_size = 18,
  label_fontface = "bold",
  label_x = 0, label_y = 1,
  hjust = -0.1, vjust = 1.1
)

# 添加总标题
final_plot_with_title <- plot_grid(
  ggdraw() +
    draw_label("Expression Patterns of Aggravation Molecules",
               fontface = "bold", size = 18),
  final_plot,
  ncol = 1,
  rel_heights = c(0.05, 1)
)

# =============================================================================
# 8. 保存图表
# =============================================================================

cat("正在保存图表...\n")

# 保存高质量PNG
ggsave("Figure4_R_Version.png",
       plot = final_plot_with_title,
       width = 16, height = 6,
       dpi = 300,
       bg = "white")

# 保存PDF
ggsave("Figure4_R_Version.pdf",
       plot = final_plot_with_title,
       width = 16, height = 6,
       device = "pdf",
       bg = "white")

# =============================================================================
# 9. 生成分析报告
# =============================================================================

cat("\n生成分析报告...\n")

# 统计信息
baseline_samples <- nrow(baseline_group)
longitudinal_samples <- nrow(longitudinal_group)

# 创建报告
report <- sprintf("
# Figure 4 分析报告 - R语言版本

## 数据概览
- 基线样本数量: %d (Simple VH vs SRD+VH)
- 纵向样本数量: %d (Baseline vs Post-anti-VEGF)

## 目标分子
- Panel A: CAST (蛋白质) - Aggravation型
- Panel B: GALNS (蛋白质) - Aggravation型
- Panel C: 3-Hydroxypropanoic Acid (代谢物) - Aggravation型

## 图表设计
- 每个分子包含4个箱线小提琴散点图组合
- 前两个：Simple VH vs SRD+VH (基线比较)
- 后两个：Baseline vs Post-anti-VEGF (治疗前后比较)
- 小提琴图：显示数据分布形状（宽度0.6，更精致）
- 箱线图：显示统计摘要（白色填充，黑色边框，宽度0.25）
- 散点：大小增加，黑色细圈，显示原始数据点
- 连线：治疗前后配对样本连线，连接实际散点位置
- 颜色方案：两种颜色（蓝色-基线比较，红色-治疗比较）
- 面板标签：A、B、C放在每个子图坐标系外面的左上角
- 统计检验：Simple VH vs SRD+VH（独立t检验），Baseline vs Post-anti-VEGF（配对t检验）
- P值显示：在坐标系内顶端显示具体P值（保留3位小数）
- 趋势指示：显示up（上调）或down（下调）趋势

## 颜色方案
- Simple VH: 蓝色 (#1F78B4)
- SRD+VH: 红色 (#E31A1C)
- Baseline: 橙色 (#FF7F00)
- Post-anti-VEGF: 绿色 (#33A02C)

## 输出文件
- Figure4_R_Version.png (高质量PNG格式)
- Figure4_R_Version.pdf (PDF格式)
- Figure4_R_Analysis_Report.md (分析报告)

## 分析完成时间
%s
",
baseline_samples, longitudinal_samples, Sys.time()
)

# 保存报告
writeLines(report, "Figure4_R_Analysis_Report.md")

cat("\n✅ Figure 4 分析完成！\n")
cat("📁 输出文件:\n")
cat("  - Figure4_R_Version.png\n")
cat("  - Figure4_R_Version.pdf\n")
cat("  - Figure4_R_Analysis_Report.md\n")

cat("\n🎉 R语言版本的Figure 4生成成功！\n")
