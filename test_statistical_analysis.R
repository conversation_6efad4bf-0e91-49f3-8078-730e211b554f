#!/usr/bin/env Rscript
# 测试统计分析功能
# Test statistical analysis functionality

library(readxl)
library(dplyr)
library(tidyr)
library(ggplot2)
library(ggpubr)
library(stringr)

cat("=== 测试统计分析功能 ===\n\n")

# 加载数据
cat("1. 加载数据...\n")
baseline_protein <- read_excel("基线蛋白组学(1-stage2_vs_1-stage1_annotation).xlsx")
baseline_group <- read_excel("基线分组情况（1-stage2_vs_1-stage1_group）.xlsx")
longitudinal_protein <- read_excel("纵向蛋白组学（2_vs_1_annotation）.xlsx")
longitudinal_group <- read_excel("纵向分组情况（2_vs_1_group）.xlsx")

# 提取CAST数据的函数
extract_molecule_data <- function(data, group_data, molecule_name, molecule_type = "protein") {
  sample_cols <- grep("--", colnames(data), value = TRUE)
  
  if (molecule_type == "protein") {
    molecule_row <- data %>% filter(Gene == molecule_name)
    if (nrow(molecule_row) == 0) {
      cat(sprintf("警告：未找到蛋白质 %s\n", molecule_name))
      return(NULL)
    }
  } else {
    molecule_row <- data %>% filter(Compounds == molecule_name)
    if (nrow(molecule_row) == 0) {
      cat(sprintf("警告：未找到代谢物 %s\n", molecule_name))
      return(NULL)
    }
  }
  
  expression_data <- molecule_row %>%
    select(all_of(sample_cols)) %>%
    pivot_longer(cols = everything(), names_to = "Sample", values_to = "Expression") %>%
    mutate(Expression = as.numeric(Expression))
  
  expression_data <- expression_data %>%
    left_join(group_data %>% select(Sample, Group), by = "Sample") %>%
    mutate(Molecule = molecule_name)
  
  return(expression_data)
}

# 提取CAST数据
cat("2. 提取CAST数据...\n")
baseline_cast <- extract_molecule_data(baseline_protein, baseline_group, "CAST", "protein")
longitudinal_cast <- extract_molecule_data(longitudinal_protein, longitudinal_group, "CAST", "protein")

if (!is.null(baseline_cast) && !is.null(longitudinal_cast)) {
  cat("CAST数据提取成功\n")
  
  # 准备基线数据
  baseline_plot_data <- baseline_cast %>%
    mutate(
      Group_Label = case_when(
        Group == "1-stage1" ~ "Simple VH",
        Group == "1-stage2" ~ "SRD+VH",
        TRUE ~ as.character(Group)
      )
    ) %>%
    filter(!is.na(Expression))
  
  # 准备纵向数据
  longitudinal_plot_data <- longitudinal_cast %>%
    mutate(
      Group_Label = case_when(
        Group == 1 ~ "Baseline",
        Group == 2 ~ "Post-anti-VEGF",
        TRUE ~ as.character(Group)
      ),
      Pair_ID = str_extract(Sample, "^[0-9]+")
    ) %>%
    filter(!is.na(Expression))
  
  cat("3. 数据统计:\n")
  cat("基线数据分组:\n")
  print(baseline_plot_data %>% count(Group_Label))
  cat("纵向数据分组:\n")
  print(longitudinal_plot_data %>% count(Group_Label))
  
  # 测试独立t检验
  cat("\n4. 测试独立t检验 (Simple VH vs SRD+VH):\n")
  simple_vh_data <- baseline_plot_data %>% filter(Group_Label == "Simple VH") %>% pull(Expression)
  srd_vh_data <- baseline_plot_data %>% filter(Group_Label == "SRD+VH") %>% pull(Expression)
  
  t_test_result1 <- t.test(simple_vh_data, srd_vh_data, paired = FALSE)
  cat("P值:", t_test_result1$p.value, "\n")
  
  # 测试配对t检验
  cat("\n5. 测试配对t检验 (Baseline vs Post-anti-VEGF):\n")
  # 确保数据是配对的
  paired_data <- longitudinal_plot_data %>%
    filter(!is.na(Pair_ID)) %>%
    group_by(Pair_ID) %>%
    filter(n() == 2) %>%  # 只保留有完整配对的数据
    ungroup()
  
  cat("配对数据点数:", nrow(paired_data), "\n")
  cat("配对数:", length(unique(paired_data$Pair_ID)), "\n")
  
  if (nrow(paired_data) > 0) {
    baseline_values <- paired_data %>% filter(Group_Label == "Baseline") %>% arrange(Pair_ID) %>% pull(Expression)
    post_values <- paired_data %>% filter(Group_Label == "Post-anti-VEGF") %>% arrange(Pair_ID) %>% pull(Expression)
    
    if (length(baseline_values) == length(post_values) && length(baseline_values) > 1) {
      t_test_result2 <- t.test(baseline_values, post_values, paired = TRUE)
      cat("P值:", t_test_result2$p.value, "\n")
    } else {
      cat("配对数据不匹配或数据不足\n")
    }
  }
  
  # 创建简单的测试图
  cat("\n6. 创建测试图...\n")
  
  # 只测试基线比较
  p_baseline <- ggplot(baseline_plot_data, aes(x = Group_Label, y = Expression, fill = Group_Label)) +
    geom_violin(alpha = 0.5, width = 0.6) +
    geom_boxplot(width = 0.2, alpha = 1, fill = "white", color = "black") +
    geom_jitter(width = 0.1, size = 2, alpha = 0.7) +
    stat_compare_means(
      comparisons = list(c("Simple VH", "SRD+VH")),
      method = "t.test",
      paired = FALSE,
      label = "p.format"
    ) +
    labs(title = "CAST 基线比较测试", x = "组别", y = "表达水平") +
    theme_bw() +
    theme(legend.position = "none")
  
  ggsave("test_baseline_stats.png", plot = p_baseline, width = 8, height = 6, dpi = 300)
  cat("基线比较测试图已保存: test_baseline_stats.png\n")
  
} else {
  cat("错误：无法提取CAST数据\n")
}

cat("\n=== 测试完成 ===\n")
