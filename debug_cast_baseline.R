#!/usr/bin/env Rscript
# 调试CAST baseline数据的箱线图问题
# Debug script for CAST baseline boxplot issue

library(readxl)
library(dplyr)
library(tidyr)
library(ggplot2)
library(stringr)

cat("=== 调试CAST baseline箱线图问题 ===\n\n")

# 加载数据
cat("1. 加载数据...\n")
baseline_protein <- read_excel("基线蛋白组学(1-stage2_vs_1-stage1_annotation).xlsx")
baseline_group <- read_excel("基线分组情况（1-stage2_vs_1-stage1_group）.xlsx")
longitudinal_protein <- read_excel("纵向蛋白组学（2_vs_1_annotation）.xlsx")
longitudinal_group <- read_excel("纵向分组情况（2_vs_1_group）.xlsx")

# 提取CAST数据的函数
extract_molecule_data <- function(data, group_data, molecule_name, molecule_type = "protein") {
  sample_cols <- grep("--", colnames(data), value = TRUE)
  
  if (molecule_type == "protein") {
    molecule_row <- data %>% filter(Gene == molecule_name)
    if (nrow(molecule_row) == 0) {
      cat(sprintf("警告：未找到蛋白质 %s\n", molecule_name))
      return(NULL)
    }
  } else {
    molecule_row <- data %>% filter(Compounds == molecule_name)
    if (nrow(molecule_row) == 0) {
      cat(sprintf("警告：未找到代谢物 %s\n", molecule_name))
      return(NULL)
    }
  }
  
  expression_data <- molecule_row %>%
    select(all_of(sample_cols)) %>%
    pivot_longer(cols = everything(), names_to = "Sample", values_to = "Expression") %>%
    mutate(Expression = as.numeric(Expression))
  
  expression_data <- expression_data %>%
    left_join(group_data %>% select(Sample, Group), by = "Sample") %>%
    mutate(Molecule = molecule_name)
  
  return(expression_data)
}

# 提取CAST基线数据
cat("2. 提取CAST基线数据...\n")
baseline_cast <- extract_molecule_data(baseline_protein, baseline_group, "CAST", "protein")

if (!is.null(baseline_cast)) {
  cat("CAST基线数据提取成功\n")
  cat("数据维度:", nrow(baseline_cast), "行\n")
  
  # 检查数据内容
  cat("\n3. 检查CAST基线数据内容:\n")
  print(head(baseline_cast, 10))
  
  # 检查分组情况
  cat("\n4. CAST基线数据分组统计:\n")
  group_summary <- baseline_cast %>% 
    filter(!is.na(Expression)) %>%
    count(Group, name = "count")
  print(group_summary)
  
  # 检查表达值分布
  cat("\n5. CAST基线表达值统计:\n")
  expr_summary <- baseline_cast %>% 
    filter(!is.na(Expression)) %>%
    group_by(Group) %>%
    summarise(
      count = n(),
      min = min(Expression, na.rm = TRUE),
      q1 = quantile(Expression, 0.25, na.rm = TRUE),
      median = median(Expression, na.rm = TRUE),
      q3 = quantile(Expression, 0.75, na.rm = TRUE),
      max = max(Expression, na.rm = TRUE),
      .groups = 'drop'
    )
  print(expr_summary)
  
  # 准备绘图数据
  cat("\n6. 准备绘图数据...\n")
  baseline_plot_data <- baseline_cast %>%
    filter(!is.na(Expression)) %>%
    mutate(
      Group_Label = case_when(
        Group == "1-stage1" ~ "Simple VH",
        Group == "1-stage2" ~ "SRD+VH",
        TRUE ~ as.character(Group)
      )
    )
  
  cat("绘图数据分组:\n")
  print(baseline_plot_data %>% count(Group_Label))
  
  # 创建测试图
  cat("\n7. 创建测试图...\n")
  
  # 设置颜色
  colors <- list(
    svh = "#1F78B4",
    srd_vh = "#E31A1C"
  )
  
  # 只绘制基线数据的箱线图
  p_test <- ggplot(baseline_plot_data, aes(x = Group_Label, y = Expression, fill = Group_Label)) +
    geom_violin(alpha = 0.5, trim = FALSE, scale = "width", width = 0.6) +
    geom_boxplot(width = 0.2, alpha = 1, outlier.shape = NA, 
                 color = "black", linewidth = 0.8, fill = "white") +
    geom_jitter(size = 3, alpha = 0.8, width = 0.1, stroke = 1, shape = 21, color = "black") +
    scale_fill_manual(values = c("Simple VH" = colors$svh, "SRD+VH" = colors$srd_vh)) +
    labs(title = "CAST 基线数据测试图", x = "组别", y = "表达水平") +
    theme_bw() +
    theme(legend.position = "none")
  
  ggsave("debug_cast_baseline_test.png", plot = p_test, width = 8, height = 6, dpi = 300)
  cat("测试图已保存: debug_cast_baseline_test.png\n")
  
} else {
  cat("错误：无法提取CAST基线数据\n")
}

cat("\n=== 调试完成 ===\n")
