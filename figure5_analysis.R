#!/usr/bin/env Rscript
# Figure 5 Analysis Script - Violin Plots for Improvement Molecules
# 绘制35个Improvement分子的小提琴图，包括3个蛋白质和32个代谢物
# 排列为6行，每行6个图（最后一行5个图）
# 第一行：A1-A6，第二行：B1-B6，...，第六行：F1-F5
# 蛋白质用蓝色，代谢物用红色
#
# Author: AI Assistant
# Date: 2025-08-03
# Version: 1.0

# =============================================================================
# 1. 环境准备和包加载
# =============================================================================

# 检查并安装必要的包
required_packages <- c(
  "readxl",        # Excel文件读取
  "dplyr",         # 数据处理
  "tibble",        # 数据框操作
  "tidyr",         # 数据重塑
  "stringr",       # 字符串处理
  "ggplot2",       # 基础绘图
  "ggpubr",        # 发表质量图表
  "cowplot",       # 图表组合
  "viridis",       # 颜色方案
  "scales"         # 坐标轴格式化
)

# 安装缺失的包
new_packages <- required_packages[!(required_packages %in% installed.packages()[,"Package"])]
if(length(new_packages)) {
  install.packages(new_packages, repos = "https://cran.rstudio.com/")
}

# 加载包
suppressPackageStartupMessages({
  library(readxl)
  library(dplyr)
  library(tibble)
  library(tidyr)
  library(stringr)
  library(ggplot2)
  library(ggpubr)
  library(cowplot)
  library(viridis)
  library(scales)
})

# =============================================================================
# 2. 数据加载和预处理
# =============================================================================

cat("正在加载数据...\n")

# 加载基线数据 (Figure 1: VH vs SRD+VH)
baseline_protein_data <- read_excel("基线蛋白组学(1-stage2_vs_1-stage1_annotation).xlsx")
baseline_metabolite_data <- read_excel("基线代谢组学（1-stage2_vs_1-stage1_info）.xlsx")
baseline_group_data <- read_excel("基线分组情况（1-stage2_vs_1-stage1_group）.xlsx")

# 加载纵向数据 (Figure 2: Post-Anti-VEGF vs Baseline)
longitudinal_protein_data <- read_excel("纵向蛋白组学（2_vs_1_annotation）.xlsx")
longitudinal_metabolite_data <- read_excel("纵向代谢组学（2_vs_1_info）.xlsx")
longitudinal_group_data <- read_excel("纵向分组情况（2_vs_1_group）.xlsx")

cat(sprintf("数据加载完成:\n"))
cat(sprintf("- 基线蛋白质数据: %d行 x %d列\n", nrow(baseline_protein_data), ncol(baseline_protein_data)))
cat(sprintf("- 基线代谢物数据: %d行 x %d列\n", nrow(baseline_metabolite_data), ncol(baseline_metabolite_data)))
cat(sprintf("- 纵向蛋白质数据: %d行 x %d列\n", nrow(longitudinal_protein_data), ncol(longitudinal_protein_data)))
cat(sprintf("- 纵向代谢物数据: %d行 x %d列\n", nrow(longitudinal_metabolite_data), ncol(longitudinal_metabolite_data)))

# =============================================================================
# 3. 定义目标分子列表
# =============================================================================

# 定义35个improvement分子
improvement_molecules <- list(
  # 3个蛋白质
  proteins = list(
    list(name = "ALB", accession = "D6RHD5", gene = "ALB"),
    list(name = "ABHD12B", accession = "Q7Z5M8", gene = "ABHD12B"),
    list(name = "NIBAN3", accession = "Q86XR2", gene = "NIBAN3")
  ),
  # 32个代谢物
  metabolites = c(
    "L-threo-3-Methylaspartate",
    "L-Glutamic Acid",
    "Inosine",
    "3-Methyl-2-Oxobutanoic Acid",
    "3-(3-Hydroxyphenyl)-3-hydroxypropanoic acid",
    "3,4-Dimethoxycinnamic acid",
    "Pyridoxal phosphate",
    "Tryptamine",
    "Indoleacetaldehyde",
    "D-Ribono-1,4-lactone",
    "3-aminobenzamide",
    "8-Azaadenosine",
    "Acetylsulfamethoxazole",
    "(+)-7-iso-Jasmonic acid",
    "Isopalmitic acid",
    "9-[(3S,4S,5R)-3,4-dihydroxy-5-(hydroxymethyl)oxolan-2-yl]-1H-purin-6-one",
    "5-(3',4',5'-Trihydroxyphenyl)-gamma-valerolactone-3'-O-sulphate",
    "L-selenomethionine",
    "Methyl (methylthio)acetate",
    "Tyrosyl-tryptophan",
    "Hydroxyphenyllactic acid",
    "6-Cyano-7-nitroquinoxaline-2,3-dione",
    "Isosorbide mononitrate",
    "Lonidamine",
    "Thioctic acid",
    "Methyl 1-(1-propenylthio)propyl disulfide",
    "2,3-Epoxyaflatoxin B1",
    "(1E,4Z,6E)-5-hydroxy-1,7-bis(4-hydroxyphenyl)hepta-1,4,6-trien-3-one",
    "6,8a-Seco-6,8a-deoxy-5-oxoavermectin''1b'' aglycone",
    "Selenocysteine",
    "(S)-2-acetamido-6-oxopimelic acid",
    "Serotonin O-sulfate"
  )
)

# 创建完整的分子列表（先蛋白质，后代谢物）
all_molecules <- c()
molecule_types <- c()

# 添加蛋白质
for(protein in improvement_molecules$proteins) {
  all_molecules <- c(all_molecules, protein$gene)
  molecule_types <- c(molecule_types, "protein")
}

# 添加代谢物
for(metabolite in improvement_molecules$metabolites) {
  all_molecules <- c(all_molecules, metabolite)
  molecule_types <- c(molecule_types, "metabolite")
}

cat(sprintf("总共需要绘制 %d 个分子\n", length(all_molecules)))
cat(sprintf("其中蛋白质 %d 个，代谢物 %d 个\n", 
            sum(molecule_types == "protein"), 
            sum(molecule_types == "metabolite")))

# =============================================================================
# 4. 设置绘图主题和颜色
# =============================================================================

# 设置期刊发表质量的主题
theme_publication <- theme_bw() +
  theme(
    text = element_text(size = 10),
    axis.title = element_text(size = 11, face = "bold"),
    axis.text = element_text(size = 9),
    plot.title = element_text(size = 12, face = "bold", hjust = 0.5),
    legend.text = element_text(size = 9),
    legend.title = element_text(size = 10, face = "bold"),
    panel.grid.major = element_line(color = "grey90", linewidth = 0.3),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(color = "black", linewidth = 0.8),
    strip.background = element_rect(fill = "white", color = "black"),
    strip.text = element_text(size = 9, face = "bold"),
    axis.text.x = element_text(angle = 45, hjust = 1, size = 8),
    plot.margin = margin(5, 5, 5, 5)
  )

cat("\n绘图环境设置完成\n")

# =============================================================================
# 5. 创建小提琴图函数
# =============================================================================

create_violin_plot_simple <- function(molecule_data_baseline, molecule_data_longitudinal,
                                     baseline_groups, longitudinal_groups,
                                     molecule_name, molecule_type = "protein") {

  cat(sprintf("正在创建%s的小提琴图...\n", molecule_name))

  # 处理长名称，自动换行
  format_title <- function(name) {
    # 如果名称长度超过30个字符，尝试在合适的位置换行
    if(nchar(name) > 30) {
      # 寻找合适的断点：空格、连字符、括号等
      break_points <- c(
        gregexpr(" ", name)[[1]],
        gregexpr("-", name)[[1]],
        gregexpr("\\(", name)[[1]],
        gregexpr(",", name)[[1]]
      )
      break_points <- break_points[break_points > 0 & break_points < nchar(name)]

      if(length(break_points) > 0) {
        # 找到最接近中间位置的断点
        mid_point <- nchar(name) / 2
        best_break <- break_points[which.min(abs(break_points - mid_point))]

        # 在断点处插入换行符
        line1 <- substr(name, 1, best_break)
        line2 <- substr(name, best_break + 1, nchar(name))
        return(paste0(line1, "\n", line2))
      }
    }
    return(name)
  }

  formatted_title <- format_title(molecule_name)

  # 统计检验函数
  perform_t_test <- function(data, group_col, value_col, paired = FALSE) {
    if(paired) {
      # 配对t检验
      groups <- unique(data[[group_col]])
      group1_data <- data[data[[group_col]] == groups[1], value_col]
      group2_data <- data[data[[group_col]] == groups[2], value_col]
      if(length(group1_data) == length(group2_data) && length(group1_data) > 1) {
        test_result <- t.test(group1_data, group2_data, paired = TRUE)
        # 计算趋势：比较第二组相对于第一组的变化
        mean1 <- mean(group1_data, na.rm = TRUE)
        mean2 <- mean(group2_data, na.rm = TRUE)
        trend <- ifelse(mean2 > mean1, "Up", "Down")
        return(list(p_value = test_result$p.value, method = "Paired t-test",
                   trend = trend, mean1 = mean1, mean2 = mean2))
      }
    } else {
      # 独立t检验
      if(length(unique(data[[group_col]])) == 2) {
        test_result <- t.test(data[[value_col]] ~ data[[group_col]])
        # 计算趋势：比较两组均值
        groups <- unique(data[[group_col]])
        mean1 <- mean(data[data[[group_col]] == groups[1], value_col], na.rm = TRUE)
        mean2 <- mean(data[data[[group_col]] == groups[2], value_col], na.rm = TRUE)
        trend <- ifelse(mean2 > mean1, "Up", "Down")
        return(list(p_value = test_result$p.value, method = "Independent t-test",
                   trend = trend, mean1 = mean1, mean2 = mean2))
      }
    }
    return(list(p_value = NA, method = "No test", trend = "", mean1 = NA, mean2 = NA))
  }

  # 格式化P值和趋势
  format_p_value_with_trend <- function(p, trend) {
    if(is.na(p)) return("P = NA")
    p_text <- if(p < 0.001) "P < 0.001" else sprintf("P = %.3f", p)
    return(paste0(p_text, " (", trend, ")"))  # 格式: P = XXX (趋势)
  }

  # 获取样本列（包含"--"的列）
  baseline_sample_cols <- grep("--", colnames(molecule_data_baseline), value = TRUE)
  longitudinal_sample_cols <- grep("--", colnames(molecule_data_longitudinal), value = TRUE)

  # 准备基线数据（VH vs SRD+VH）
  baseline_values <- as.numeric(molecule_data_baseline[1, baseline_sample_cols])
  baseline_samples <- baseline_sample_cols
  baseline_group_info <- baseline_groups$Group[match(baseline_samples, baseline_groups$Sample)]

  baseline_df <- data.frame(
    Sample = baseline_samples,
    Value = baseline_values,
    Group = ifelse(baseline_group_info == "1-stage1", "Simple VH", "SRD+VH"),
    Comparison = "Baseline Comparison",
    stringsAsFactors = FALSE
  ) %>%
    filter(!is.na(Value) & !is.na(Group))

  # 准备纵向数据（治疗前后）
  # 提取治疗前（--1）和治疗后（--2）的数据
  pre_treatment_cols <- grep("--1$", longitudinal_sample_cols, value = TRUE)
  post_treatment_cols <- grep("--2$", longitudinal_sample_cols, value = TRUE)

  # 匹配治疗前后的样本
  pre_values <- as.numeric(molecule_data_longitudinal[1, pre_treatment_cols])
  post_values <- as.numeric(molecule_data_longitudinal[1, post_treatment_cols])

  # 创建患者ID匹配
  pre_patient_ids <- gsub("--1$", "", pre_treatment_cols)
  post_patient_ids <- gsub("--2$", "", post_treatment_cols)

  # 找到匹配的患者
  matched_patients <- intersect(pre_patient_ids, post_patient_ids)

  longitudinal_df <- data.frame()

  if(length(matched_patients) > 0) {
    for(patient in matched_patients) {
      pre_col <- paste0(patient, "--1")
      post_col <- paste0(patient, "--2")

      if(pre_col %in% pre_treatment_cols && post_col %in% post_treatment_cols) {
        pre_val <- as.numeric(molecule_data_longitudinal[1, pre_col])
        post_val <- as.numeric(molecule_data_longitudinal[1, post_col])

        if(!is.na(pre_val) && !is.na(post_val)) {
          longitudinal_df <- rbind(longitudinal_df,
            data.frame(
              Sample = c(pre_col, post_col),
              Value = c(pre_val, post_val),
              Group = c("Baseline", "Post-anti-VEGF"),
              Comparison = "Longitudinal Comparison",
              Patient = c(patient, patient),
              stringsAsFactors = FALSE
            )
          )
        }
      }
    }
  }

  # 合并数据
  plot_data <- rbind(
    baseline_df %>% select(Sample, Value, Group, Comparison),
    longitudinal_df %>% select(Sample, Value, Group, Comparison)
  )

  # 设置因子顺序
  plot_data$Group <- factor(plot_data$Group,
                           levels = c("Simple VH", "SRD+VH", "Baseline", "Post-anti-VEGF"))
  plot_data$Comparison <- factor(plot_data$Comparison,
                                levels = c("Baseline Comparison", "Longitudinal Comparison"))

  # 进行统计检验
  # 1. 基线比较：Simple VH vs SRD+VH (独立t检验)
  baseline_test_data <- plot_data %>%
    filter(Comparison == "Baseline Comparison" & !is.na(Value))
  baseline_test <- perform_t_test(baseline_test_data, "Group", "Value", paired = FALSE)

  # 2. 纵向比较：Baseline vs Post-anti-VEGF (配对t检验)
  longitudinal_test_data <- plot_data %>%
    filter(Comparison == "Longitudinal Comparison" & !is.na(Value))
  longitudinal_test <- perform_t_test(longitudinal_test_data, "Group", "Value", paired = TRUE)

  # 格式化P值和趋势文本
  baseline_p_text <- format_p_value_with_trend(baseline_test$p_value, baseline_test$trend)
  longitudinal_p_text <- format_p_value_with_trend(longitudinal_test$p_value, longitudinal_test$trend)

  # 准备配对连线数据（仅用于纵向比较）
  paired_data <- data.frame()
  if(nrow(longitudinal_df) > 0 && "Patient" %in% colnames(longitudinal_df)) {
    # 为每个患者生成一致的jitter位置
    set.seed(123)  # 确保jitter位置可重现
    paired_data <- longitudinal_df %>%
      filter(!is.na(Patient)) %>%
      select(Patient, Group, Value, Comparison) %>%
      group_by(Patient) %>%
      filter(n() == 2) %>%  # 确保每个患者都有两个时间点的数据
      ungroup() %>%
      arrange(Patient, Group) %>%
      group_by(Patient) %>%
      mutate(
        # 为每个患者生成一致的jitter偏移
        jitter_offset = runif(1, -0.15, 0.15),
        # 计算实际的x位置
        x_pos = case_when(
          Group == "Baseline" ~ 1 + jitter_offset,
          Group == "Post-anti-VEGF" ~ 2 + jitter_offset,
          TRUE ~ as.numeric(as.factor(Group)) + jitter_offset
        )
      ) %>%
      ungroup()
  }

  # 创建小提琴图
  p <- ggplot(plot_data, aes(x = Group, y = Value, fill = Group)) +
    geom_violin(alpha = 0.6, trim = FALSE, scale = "width") +
    geom_boxplot(width = 0.08, alpha = 0.7, outlier.shape = NA) +
    # 添加配对连线（仅在纵向比较面板中）
    {if(nrow(paired_data) > 0) {
      geom_line(data = paired_data %>%
                filter(Comparison == "Longitudinal Comparison"),
                aes(x = x_pos, y = Value, group = Patient),
                color = "black", alpha = 0.5, linewidth = 0.3, inherit.aes = FALSE)
    }} +
    # 修改散点样式：更小、有颜色填充、有黑色边框
    # 为纵向比较使用固定位置，为基线比较使用jitter
    {if(nrow(paired_data) > 0) {
      list(
        # 纵向比较的散点（固定位置，与连线对应）
        geom_point(data = paired_data %>% filter(Comparison == "Longitudinal Comparison"),
                   aes(x = x_pos, y = Value, fill = Group),
                   alpha = 0.7, size = 1.5, shape = 21, color = "black", stroke = 0.5,
                   inherit.aes = FALSE),
        # 基线比较的散点（使用jitter）
        geom_jitter(data = plot_data %>% filter(Comparison == "Baseline Comparison"),
                    aes(fill = Group), width = 0.15, alpha = 0.7, size = 1.5,
                    shape = 21, color = "black", stroke = 0.5)
      )
    } else {
      # 如果没有配对数据，所有散点都使用jitter
      geom_jitter(aes(fill = Group), width = 0.15, alpha = 0.7, size = 1.5,
                  shape = 21, color = "black", stroke = 0.5)
    }} +
    scale_fill_manual(values = {
      if(molecule_type == "protein") {
        # 蛋白质使用蓝色系
        c("Simple VH" = "#4472C4", "SRD+VH" = "#2F5597",
          "Baseline" = "#4472C4", "Post-anti-VEGF" = "#2F5597")
      } else {
        # 代谢物使用红色系
        c("Simple VH" = "#E74C3C", "SRD+VH" = "#C0392B",
          "Baseline" = "#E74C3C", "Post-anti-VEGF" = "#C0392B")
      }
    }) +
    facet_wrap(~ Comparison, scales = "free_x", ncol = 2) +
    labs(
      title = formatted_title,
      x = "",
      y = ifelse(molecule_type == "protein", "Expression", "Concentration"),
      fill = "Group"
    ) +
    theme_publication +
    theme(
      legend.position = "none",  # 移除图例
      strip.text = element_text(size = 8),
      plot.title = element_text(size = 10)
    )

  # 创建P值标注数据框
  p_value_data <- data.frame(
    Comparison = c("Baseline Comparison", "Longitudinal Comparison"),
    p_text = c(baseline_p_text, longitudinal_p_text),
    x_pos = c(1.5, 1.5),  # 在每个面板的中间位置
    stringsAsFactors = FALSE
  )

  # 添加P值标注
  p <- p +
    geom_text(data = p_value_data,
              aes(x = x_pos, y = Inf, label = p_text),
              vjust = 1.2, hjust = 0.5, size = 2.5, fontface = "bold",
              color = "black", inherit.aes = FALSE)

  return(p)
}

# =============================================================================
# 6. 分子查找函数
# =============================================================================

find_molecule_data <- function(molecule_name, molecule_type,
                              baseline_protein_data, baseline_metabolite_data,
                              longitudinal_protein_data, longitudinal_metabolite_data) {

  if(molecule_type == "protein") {
    # 查找蛋白质
    baseline_data <- baseline_protein_data %>%
      filter(str_detect(Gene, paste0("^", molecule_name, "$")) |
             str_detect(Description, molecule_name) |
             str_detect(Accession, molecule_name))

    longitudinal_data <- longitudinal_protein_data %>%
      filter(str_detect(Gene, paste0("^", molecule_name, "$")) |
             str_detect(Description, molecule_name) |
             str_detect(Accession, molecule_name))

    # 如果没有找到，尝试更宽泛的搜索
    if(nrow(baseline_data) == 0) {
      baseline_data <- baseline_protein_data %>%
        filter(str_detect(toupper(Gene), toupper(molecule_name)) |
               str_detect(toupper(Description), toupper(molecule_name)))
      longitudinal_data <- longitudinal_protein_data %>%
        filter(str_detect(toupper(Gene), toupper(molecule_name)) |
               str_detect(toupper(Description), toupper(molecule_name)))
    }

  } else {
    # 查找代谢物
    baseline_data <- baseline_metabolite_data %>%
      filter(str_detect(Compounds, fixed(molecule_name, ignore_case = TRUE)))

    longitudinal_data <- longitudinal_metabolite_data %>%
      filter(str_detect(Compounds, fixed(molecule_name, ignore_case = TRUE)))

    # 如果没有找到，尝试部分匹配
    if(nrow(baseline_data) == 0) {
      # 提取关键词进行搜索
      keywords <- strsplit(molecule_name, "[ -]")[[1]]
      keywords <- keywords[nchar(keywords) > 3]  # 只保留长度大于3的关键词

      if(length(keywords) > 0) {
        pattern <- paste(keywords, collapse = "|")
        baseline_data <- baseline_metabolite_data %>%
          filter(str_detect(toupper(Compounds), toupper(pattern)))
        longitudinal_data <- longitudinal_metabolite_data %>%
          filter(str_detect(toupper(Compounds), toupper(pattern)))
      }
    }
  }

  return(list(baseline = baseline_data, longitudinal = longitudinal_data))
}

# =============================================================================
# 7. 批量创建小提琴图
# =============================================================================

cat("\n开始创建所有小提琴图...\n")

# 创建空的图列表
violin_plots <- list()
plot_labels <- c()

# 生成标签（A1-A6, B1-B6, ..., F1-F5）
row_letters <- LETTERS[1:6]
for(i in 1:length(all_molecules)) {
  row_idx <- ceiling(i / 6)
  col_idx <- ((i - 1) %% 6) + 1
  plot_labels <- c(plot_labels, paste0(row_letters[row_idx], col_idx))
}

# 为每个分子创建小提琴图
for(i in 1:length(all_molecules)) {
  molecule_name <- all_molecules[i]
  molecule_type <- molecule_types[i]
  label <- plot_labels[i]

  cat(sprintf("正在处理 %s (%s) - %s...\n", molecule_name, molecule_type, label))

  # 查找分子数据
  molecule_data <- find_molecule_data(molecule_name, molecule_type,
                                     baseline_protein_data, baseline_metabolite_data,
                                     longitudinal_protein_data, longitudinal_metabolite_data)

  # 检查是否找到数据
  if(nrow(molecule_data$baseline) > 0 && nrow(molecule_data$longitudinal) > 0) {
    # 创建小提琴图
    violin_plots[[label]] <- create_violin_plot_simple(
      molecule_data$baseline, molecule_data$longitudinal,
      baseline_group_data, longitudinal_group_data,
      molecule_name, molecule_type
    )
    cat(sprintf("  ✓ %s 小提琴图创建完成\n", label))
  } else {
    # 创建占位图
    violin_plots[[label]] <- ggplot() +
      annotate("text", x = 0.5, y = 0.5,
               label = paste(molecule_name, "not found"),
               size = 3, color = "red") +
      labs(title = molecule_name) +
      theme_void() +
      theme(plot.title = element_text(size = 10, hjust = 0.5))
    cat(sprintf("  ⚠ %s 数据未找到，创建占位图\n", label))
  }
}

cat(sprintf("\n所有小提琴图创建完成，共 %d 个图\n", length(violin_plots)))

# =============================================================================
# 8. 整合所有图表
# =============================================================================

cat("\n正在整合所有图表...\n")

# 按照6x6的网格排列（最后一行只有5个）
# 第一行：A1-A6
# 第二行：B1-B6
# 第三行：C1-C6
# 第四行：D1-D6
# 第五行：E1-E6
# 第六行：F1-F5

# 创建6行的图表
row_plots <- list()

for(row in 1:6) {
  start_idx <- (row - 1) * 6 + 1
  end_idx <- min(row * 6, length(violin_plots))

  row_labels <- plot_labels[start_idx:end_idx]
  row_violin_plots <- violin_plots[row_labels]

  if(row == 6) {
    # 最后一行只有5个图，添加空白图
    empty_plot <- ggplot() + theme_void()
    row_violin_plots <- c(row_violin_plots, list(empty_plot))
    row_labels <- c(row_labels, "")
  }

  # 创建这一行的图
  row_plots[[row]] <- plot_grid(
    plotlist = row_violin_plots,
    labels = row_labels,
    label_size = 16,  # 增大字母序号字号
    label_fontface = "bold",
    ncol = 6,
    rel_widths = rep(1, 6)
  )
}

# 将所有行组合起来
final_plot <- plot_grid(
  plotlist = row_plots,
  ncol = 1,
  rel_heights = rep(1, 6)
)

# 添加总标题
final_plot_with_title <- plot_grid(
  ggdraw() +
    draw_label("Figure 5: Improvement Molecules Expression Patterns",
               fontface = "bold", size = 20),
  final_plot,
  ncol = 1,
  rel_heights = c(0.03, 1)
)

# =============================================================================
# 9. 保存图表
# =============================================================================

cat("正在保存图表...\n")

# 保存高质量PNG
ggsave("Figure5_Violin_Plots.png",
       plot = final_plot_with_title,
       width = 24, height = 20,
       dpi = 300,
       bg = "white")

# 保存PDF
ggsave("Figure5_Violin_Plots.pdf",
       plot = final_plot_with_title,
       width = 24, height = 20,
       device = "pdf",
       bg = "white")

# =============================================================================
# 10. 生成分析报告
# =============================================================================

cat("\n生成分析报告...\n")

# 统计成功创建的图表数量
successful_plots <- sum(sapply(violin_plots, function(p) {
  !("GeomText" %in% class(p$layers[[1]]$geom))
}))

# 创建报告
report <- sprintf("
# Figure 5 分析报告 - Improvement分子小提琴图

## 数据概览
- 目标分子总数: %d个
- 蛋白质: %d个 (ALB, ABHD12B, NIBAN3)
- 代谢物: %d个
- 基线样本数量: %d
- 纵向样本数量: %d

## 图表布局
- 总体布局: 6行 × 6列 (最后一行5个图)
- 第一行: A1-A6
- 第二行: B1-B6
- 第三行: C1-C6
- 第四行: D1-D6
- 第五行: E1-E6
- 第六行: F1-F5

## 分子列表
### 蛋白质 (蓝色)
1. A1: ALB (D6RHD5)
2. A2: ABHD12B (Q7Z5M8)
3. A3: NIBAN3 (Q86XR2)

### 代谢物 (红色)
4. A4: L-threo-3-Methylaspartate
5. A5: L-Glutamic Acid
6. A6: Inosine
7. B1: 3-Methyl-2-Oxobutanoic Acid
8. B2: 3-(3-Hydroxyphenyl)-3-hydroxypropanoic acid
9. B3: 3,4-Dimethoxycinnamic acid
10. B4: Pyridoxal phosphate
11. B5: Tryptamine
12. B6: Indoleacetaldehyde
13. C1: D-Ribono-1,4-lactone
14. C2: 3-aminobenzamide
15. C3: 8-Azaadenosine
16. C4: Acetylsulfamethoxazole
17. C5: (+)-7-iso-Jasmonic acid
18. C6: Isopalmitic acid
19. D1: 9-[(3S,4S,5R)-3,4-dihydroxy-5-(hydroxymethyl)oxolan-2-yl]-1H-purin-6-one
20. D2: 5-(3',4',5'-Trihydroxyphenyl)-gamma-valerolactone-3'-O-sulphate
21. D3: L-selenomethionine
22. D4: Methyl (methylthio)acetate
23. D5: Tyrosyl-tryptophan
24. D6: Hydroxyphenyllactic acid
25. E1: 6-Cyano-7-nitroquinoxaline-2,3-dione
26. E2: Isosorbide mononitrate
27. E3: Lonidamine
28. E4: Thioctic acid
29. E5: Methyl 1-(1-propenylthio)propyl disulfide
30. E6: 2,3-Epoxyaflatoxin B1
31. F1: (1E,4Z,6E)-5-hydroxy-1,7-bis(4-hydroxyphenyl)hepta-1,4,6-trien-3-one
32. F2: 6,8a-Seco-6,8a-deoxy-5-oxoavermectin''1b'' aglycone
33. F3: Selenocysteine
34. F4: (S)-2-acetamido-6-oxopimelic acid
35. F5: Serotonin O-sulfate

## 图表说明
每个小提琴图包含4个部分：
- 左侧两个小提琴: Simple VH组 vs SRD+VH组 (基线比较)
- 右侧两个小提琴: 治疗前 vs 治疗后 (纵向比较)
- 蛋白质使用蓝色系，代谢物使用红色系
- 纵向比较中包含配对连线显示个体变化趋势

## 数据处理结果
- 成功创建图表: %d个
- 数据缺失图表: %d个

## 输出文件
- Figure5_Violin_Plots.png (高质量PNG格式, 24×20英寸)
- Figure5_Violin_Plots.pdf (PDF格式, 24×20英寸)

## 分析完成时间
%s
",
length(all_molecules),
sum(molecule_types == "protein"),
sum(molecule_types == "metabolite"),
nrow(baseline_group_data),
nrow(longitudinal_group_data)/2,  # 纵向数据是成对的
successful_plots,
length(violin_plots) - successful_plots,
Sys.time()
)

# 保存报告
writeLines(report, "Figure5_Analysis_Report.md")

cat("\n✅ Figure 5 分析完成！\n")
cat("📁 输出文件:\n")
cat("  - Figure5_Violin_Plots.png\n")
cat("  - Figure5_Violin_Plots.pdf\n")
cat("  - Figure5_Analysis_Report.md\n")

cat("\n🎉 Figure 5 - 35个Improvement分子小提琴图生成成功！\n")
cat(sprintf("📊 成功创建 %d 个图表，%d 个数据缺失\n",
            successful_plots, length(violin_plots) - successful_plots))
