#!/usr/bin/env Rscript
# 演示箱线小提琴散点图组合效果
# Demo script for boxplot + violin + scatter plot combination

library(ggplot2)
library(dplyr)

# 创建示例数据
set.seed(123)
demo_data <- data.frame(
  Group = rep(c("Simple VH", "SRD+VH", "Baseline", "Post-anti-VEGF"), each = 20),
  Expression = c(
    rnorm(20, mean = 5, sd = 1),    # Simple VH
    rnorm(20, mean = 6, sd = 1.2),  # SRD+VH
    rnorm(20, mean = 4.5, sd = 0.8), # Baseline
    rnorm(20, mean = 7, sd = 1.1)   # Post-anti-VEGF
  )
)

# 添加抖动位置
demo_data <- demo_data %>%
  mutate(
    Group_Numeric = case_when(
      Group == "Simple VH" ~ 1,
      Group == "SRD+VH" ~ 2,
      Group == "Baseline" ~ 3,
      Group == "Post-anti-VEGF" ~ 4
    ),
    jitter_offset = runif(n(), -0.2, 0.2),
    x_jittered = Group_Numeric + jitter_offset
  )

# 设置颜色（两种颜色：基线比较 vs 治疗比较）
colors <- c(
  "Simple VH" = "#1F78B4",      # 蓝色 - 基线比较
  "SRD+VH" = "#1F78B4",        # 蓝色 - 基线比较
  "Baseline" = "#E31A1C",      # 红色 - 治疗比较
  "Post-anti-VEGF" = "#E31A1C" # 红色 - 治疗比较
)

# 创建箱线小提琴散点图
p <- ggplot(demo_data, aes(x = Group, y = Expression, fill = Group)) +
  # 小提琴图 - 显示数据分布形状（调窄宽度）
  geom_violin(alpha = 0.5, trim = FALSE, scale = "width", width = 0.6) +
  # 箱线图 - 显示统计摘要（白色填充，更明显）
  geom_boxplot(width = 0.2, alpha = 1, outlier.shape = NA,
               color = "black", linewidth = 0.8, fill = "white") +
  # 散点 - 显示原始数据点
  geom_point(aes(x = x_jittered), size = 3, alpha = 0.8, stroke = 1, shape = 21, color = "black") +
  scale_fill_manual(values = colors) +
  labs(
    title = "箱线小提琴散点图组合示例",
    subtitle = "小提琴图显示分布形状，箱线图显示统计摘要，散点显示原始数据",
    x = "组别",
    y = "表达水平"
  ) +
  theme_bw() +
  theme(
    legend.position = "none",
    axis.text.x = element_text(angle = 45, hjust = 1),
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12)
  )

# 保存示例图
ggsave("demo_boxplot_violin.png", plot = p, width = 10, height = 6, dpi = 300)

cat("✅ 箱线小提琴散点图组合示例已生成：demo_boxplot_violin.png\n")
cat("\n📊 图表说明:\n")
cat("- 小提琴图（半透明填充）：显示数据的概率密度分布\n")
cat("- 箱线图（黑色边框）：显示中位数、四分位数和异常值\n")
cat("- 散点（黑圈）：显示每个原始数据点的精确位置\n")
cat("- 这种组合提供了最全面的数据可视化信息\n")
