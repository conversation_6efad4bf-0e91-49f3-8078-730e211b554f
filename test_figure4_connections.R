#!/usr/bin/env Rscript
# 测试Figure 4连线是否正确连接散点
# Test script to verify Figure 4 connections

library(readxl)
library(dplyr)
library(tidyr)
library(ggplot2)
library(stringr)

# 加载数据
longitudinal_protein <- read_excel("纵向蛋白组学（2_vs_1_annotation）.xlsx")
longitudinal_group <- read_excel("纵向分组情况（2_vs_1_group）.xlsx")

# 提取CAST数据作为示例
cast_data <- longitudinal_protein %>% 
  filter(Gene == "CAST") %>%
  select(contains("--")) %>%
  pivot_longer(cols = everything(), names_to = "Sample", values_to = "Expression") %>%
  mutate(Expression = as.numeric(Expression)) %>%
  left_join(longitudinal_group %>% select(Sample, Group), by = "Sample") %>%
  mutate(
    Group_Label = case_when(
      Group == 1 ~ "Baseline",
      Group == 2 ~ "Post-anti-VEGF"
    ),
    Patient_ID = str_extract(Sample, "^[^-]+")
  ) %>%
  filter(!is.na(Expression))

# 检查配对数据
paired_patients <- cast_data %>%
  group_by(Patient_ID) %>%
  filter(n() == 2) %>%
  arrange(Group) %>%
  ungroup()

cat("配对患者数量:", length(unique(paired_patients$Patient_ID)), "\n")
cat("总数据点:", nrow(paired_patients), "\n")

# 显示前几个配对样本
cat("\n前5个配对患者的数据:\n")
print(paired_patients %>% 
      arrange(Patient_ID, Group) %>% 
      head(10) %>%
      select(Patient_ID, Group_Label, Expression))

cat("\n✅ 连线测试完成！每个患者在Baseline和Post-anti-VEGF都有数据点，连线将正确连接这些配对的散点。\n")
